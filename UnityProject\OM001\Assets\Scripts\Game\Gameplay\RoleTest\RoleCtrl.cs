
using UnityEngine;
using Qarth;


namespace GameWish.Game
{
	public class RoleCtrl : MonoBehaviour
	{
		public RoleAnimCtrl animCtrl;
		public CharacterController controller;
		public string m_JoystickName = "Movement";
		private float m_WalkSpdRatio = 0.3f;
		private Vector3 m_MoveDirection = Vector3.zero;
		private Vector3 m_LastMoveDirection = Vector3.zero;
		Vector3 m_InputVec;
		Vector3 m_MoveInput;

		public float m_MaxSpeed = 10;
		protected float m_RotSpd = 1000;
		protected float m_SpdMulMod = 1f;
		protected float m_SpdAddMod = 0f;
		protected float m_RotSpdMulMod = 1f;
		protected float m_RotSpdAddMod = 0f;

		protected float m_OriginMaxPd;


		public float curSpeed
		{
			get { return m_MoveDirection.magnitude * maxSpeed; }
		}

		public float maxSpeed
		{
			get { return m_MaxSpeed * m_SpdMulMod + m_SpdAddMod; }
		}

		public float rotSpd
		{
			get { return m_RotSpd * m_RotSpdMulMod + m_RotSpdAddMod; }
		}

		public float maxSpdUpRatio
		{
			get { return maxSpeed / m_OriginMaxPd; }
		}

		protected bool m_Movable = true;

		public void SetMoveInput(Vector3 input)
		{
			m_MoveInput = input;
			m_LastMoveDirection = m_MoveDirection;
			m_MoveDirection = m_MoveInput;
		}

		// Update is called once per frame
		void Update()
		{
			if (!m_Movable) return;
			if (UltimateJoystick.GetJoystickState(m_JoystickName))
			{
				m_InputVec = new Vector3(UltimateJoystick.GetHorizontalAxis(m_JoystickName), 0,
					UltimateJoystick.GetVerticalAxis(m_JoystickName));

				if (m_InputVec != Vector3.zero)
				{
					if (m_InputVec.magnitude < 0.4f)
					{
						m_InputVec = m_InputVec.normalized * m_WalkSpdRatio;
					}
					else
					{
						m_InputVec = m_InputVec.normalized;
					}
				}

				SetMoveInput(m_InputVec);
			}
			else
				SetMoveInput(Vector3.zero);


			if (m_MoveDirection.sqrMagnitude * maxSpeed > 0)
			{
				animCtrl.speed = (curSpeed / maxSpeed);
			}
			else
			{
				animCtrl.speed = 0;
			}



		}

		Quaternion m_LookRot;
		Vector3 m_Fwd;
		void FixedUpdate()
		{
			if (!m_Movable) return;
			if (m_MoveInput == Vector3.zero)
				return;
			if (controller.isGrounded)
			{
				//Log.e("grounded");
				controller.SimpleMove(m_MoveDirection * maxSpeed * Time.fixedDeltaTime);
				m_Fwd = controller.transform.forward;
				m_LookRot = Quaternion.LookRotation(m_LastMoveDirection);
				if (Vector3.Dot(m_Fwd, m_LastMoveDirection) > 0.99)
					controller.transform.rotation = m_LookRot;
				else
					controller.transform.rotation = Quaternion.Slerp(controller.transform.rotation, m_LookRot, rotSpd * Time.fixedDeltaTime);
			}
			else
			{
				Log.e("In Air");
			}
		}


	}



}
